# 面包屑导航修复测试指南

## 问题描述
在功能测试计划详情页面，面包屑显示为 "功能测试计划详情 / 功能测试计划详情"，点击第一个"功能测试计划详情"时会报错：
```
RequestValidationError, [{'type': 'int_parsing', 'loc': ('query', 'plan_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'NaN'}]
```

## 修复内容

### 1. 数据库菜单配置修复
- 将"功能测试计划详情"菜单的 parent_id 从 0 改为 40（功能测试计划的ID）
- 将"接口测试计划详情"菜单的 parent_id 从 0 改为 29（接口测试计划的ID）
- 将详情页面菜单设置为隐藏（is_hidden = true），因为它们是动态路由
- **关键修复**：将详情页面的路径从绝对路径改为相对路径
  - 功能测试计划详情：`/test_plan/functional_test_plan/detail/:id` → `detail/:id`
  - 接口测试计划详情：`/test_plan/api_test_plan/detail/:id` → `detail/:id`

### 2. 面包屑组件优化
- 修改 `web/src/layout/components/header/components/BreadCrumb.vue`
- 添加特殊处理逻辑，为详情页面构建正确的面包屑层级
- 支持功能测试计划详情、接口测试计划详情、项目详情等页面

### 3. 路由生成逻辑优化
- 修改 `web/src/store/modules/permission/index.js`
- 添加详细的路由生成日志，便于调试
- 确保隐藏的子菜单也能正确生成路由

## 测试步骤

### 测试前准备
1. 确保后端服务运行在 http://localhost:8000
2. 确保前端服务运行在 http://localhost:3101
3. 登录系统

### 测试功能测试计划详情页面
1. 访问 http://localhost:3101
2. 打开浏览器开发者工具，查看控制台
3. 登录系统（如果尚未登录）
4. 在控制台中查找路由生成日志：
   - 🔧 生成子路由: 功能测试计划详情 -> detail/:id
   - 🔧 Adding route: 功能测试计划详情
   - ✅ 测试计划路由结构正确
5. 导航到 "测试计划" -> "功能测试计划"
6. 点击任意一个测试计划的"详情"按钮
7. **关键测试**：页面应该成功跳转到详情页面，而不是停留在列表页面
8. 观察面包屑导航，应该显示：`功能测试计划 > 功能测试计划详情`
9. 点击面包屑中的"功能测试计划"，应该正确跳转回功能测试计划列表页面
10. 不应该出现 plan_id 参数错误

### 测试接口测试计划详情页面
1. 导航到 "测试计划" -> "接口测试计划"
2. 点击任意一个测试计划的"查看详情"按钮
3. 观察面包屑导航，应该显示：`接口测试计划 > 接口测试计划详情`
4. 点击面包屑中的"接口测试计划"，应该正确跳转回接口测试计划列表页面

### 预期结果
- 面包屑导航显示正确的层级关系
- 点击面包屑中的父级菜单能正确跳转
- 不再出现 plan_id 参数解析错误
- 详情页面菜单不再显示在侧边栏中（因为设置了 is_hidden = true）

## 技术细节

### 修复原理
1. **菜单层级问题**：原来详情页面菜单的 parent_id 为 0，导致它们被视为顶级菜单
2. **面包屑逻辑问题**：原来的面包屑基于 route.matched，但对于动态路由处理不当
3. **参数传递问题**：点击带参数的路径时，没有正确处理参数缺失的情况

### 解决方案
1. **数据库层面**：修正菜单的父子关系，隐藏详情页面菜单
2. **前端层面**：在面包屑组件中添加特殊处理逻辑，为详情页面构建正确的导航路径
3. **路由处理**：在点击面包屑时，正确处理带参数的路径，避免传递无效参数

## 文件修改清单
- `web/src/layout/components/header/components/BreadCrumb.vue` - 面包屑组件优化
- 数据库 `menu` 表 - 修正菜单层级关系

## 注意事项
- 此修复适用于所有详情页面的面包屑导航
- 如果添加新的详情页面，需要在面包屑组件中添加相应的处理逻辑
- 菜单配置的修改会影响动态路由生成，确保测试完整的导航流程
