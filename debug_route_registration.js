// 在浏览器控制台中运行此脚本来调试路由注册问题

console.log('=== 开始路由调试 ===');

// 1. 检查Vue Router实例
if (window.$router) {
    console.log('✅ Vue Router实例存在');
    
    // 2. 获取所有注册的路由
    const allRoutes = window.$router.getRoutes();
    console.log('📋 所有注册的路由数量:', allRoutes.length);
    
    // 3. 查找功能测试计划相关路由
    const functionalRoutes = allRoutes.filter(route => 
        route.name && route.name.includes('功能测试计划')
    );
    
    console.log('🎯 功能测试计划相关路由:', functionalRoutes);
    
    // 4. 查找详情页面路由
    const detailRoutes = allRoutes.filter(route => 
        route.path && route.path.includes('/detail/')
    );
    
    console.log('📄 详情页面路由:', detailRoutes);
    
    // 5. 检查特定路径是否存在
    const targetPath = '/test_plan/functional_test_plan/detail/1';
    const matchedRoute = window.$router.resolve(targetPath);
    
    console.log('🔍 目标路径匹配结果:', targetPath);
    console.log('匹配的路由:', matchedRoute);
    
    // 6. 检查当前路由
    console.log('📍 当前路由:', window.$router.currentRoute.value);
    
    // 7. 尝试手动跳转测试
    console.log('🚀 尝试手动跳转测试...');
    
    // 测试函数
    window.testRouteNavigation = function(testId = 1) {
        const testPath = `/test_plan/functional_test_plan/detail/${testId}`;
        console.log(`测试跳转到: ${testPath}`);
        
        try {
            window.$router.push(testPath).then(() => {
                console.log('✅ 跳转成功');
                console.log('当前路由:', window.$router.currentRoute.value);
            }).catch(error => {
                console.error('❌ 跳转失败:', error);
            });
        } catch (error) {
            console.error('❌ 跳转异常:', error);
        }
    };
    
    console.log('💡 可以运行 testRouteNavigation() 来测试路由跳转');
    
} else {
    console.error('❌ Vue Router实例不存在');
}

// 8. 检查权限相关
if (window.$pinia) {
    console.log('✅ Pinia状态管理存在');
    
    // 检查权限store
    try {
        const permissionStore = window.$pinia._s.get('permission');
        if (permissionStore) {
            console.log('✅ 权限Store存在');
            console.log('访问路由:', permissionStore.accessRoutes);
            console.log('菜单:', permissionStore.menus);
        } else {
            console.log('❌ 权限Store不存在');
        }
    } catch (error) {
        console.log('❌ 无法访问权限Store:', error);
    }
} else {
    console.error('❌ Pinia状态管理不存在');
}

console.log('=== 路由调试完成 ===');
