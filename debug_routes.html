<!DOCTYPE html>
<html>
<head>
    <title>路由调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .route { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .hidden { background-color: #f0f0f0; }
        .test-button { margin: 5px; padding: 10px; background: #007bff; color: white; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>路由调试页面</h1>
    
    <h2>测试功能</h2>
    <button class="test-button" onclick="testFunctionalDetailRoute()">测试功能测试计划详情路由</button>
    <button class="test-button" onclick="testApiDetailRoute()">测试接口测试计划详情路由</button>
    <button class="test-button" onclick="checkAllRoutes()">检查所有路由</button>
    
    <h2>测试结果</h2>
    <div id="result"></div>
    
    <script>
        function testFunctionalDetailRoute() {
            const testUrl = '/test_plan/functional_test_plan/detail/1';
            const result = document.getElementById('result');
            
            // 模拟路由跳转测试
            result.innerHTML = `
                <div class="route">
                    <h3>功能测试计划详情路由测试</h3>
                    <p>测试URL: ${testUrl}</p>
                    <p>当前URL: ${window.location.href}</p>
                    <p>状态: 准备测试...</p>
                    <a href="${testUrl}" target="_blank">在新窗口中打开</a>
                </div>
            `;
        }
        
        function testApiDetailRoute() {
            const testUrl = '/test_plan/api_test_plan/detail/1';
            const result = document.getElementById('result');
            
            result.innerHTML = `
                <div class="route">
                    <h3>接口测试计划详情路由测试</h3>
                    <p>测试URL: ${testUrl}</p>
                    <p>当前URL: ${window.location.href}</p>
                    <p>状态: 准备测试...</p>
                    <a href="${testUrl}" target="_blank">在新窗口中打开</a>
                </div>
            `;
        }
        
        function checkAllRoutes() {
            const result = document.getElementById('result');
            result.innerHTML = `
                <div class="route">
                    <h3>路由检查说明</h3>
                    <p>请在浏览器开发者工具的控制台中查看路由生成日志</p>
                    <p>查找以下关键信息：</p>
                    <ul>
                        <li>🔧 生成子路由: 功能测试计划详情</li>
                        <li>🔧 Adding route: 功能测试计划详情</li>
                        <li>🔍 All registered routes</li>
                    </ul>
                    <p>如果看到这些日志，说明路由已正确注册</p>
                </div>
            `;
        }
    </script>
</body>
</html>
