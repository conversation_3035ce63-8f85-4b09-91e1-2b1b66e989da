<template>
  <div style="padding: 20px;">
    <h1>路由调试页面</h1>
    
    <div style="margin: 20px 0;">
      <h2>路由测试</h2>
      <n-button type="primary" @click="testFunctionalDetailRoute">
        测试功能测试计划详情路由
      </n-button>
      <n-button type="info" @click="checkAllRoutes" style="margin-left: 10px;">
        检查所有路由
      </n-button>
      <n-button type="warning" @click="checkPermissionStore" style="margin-left: 10px;">
        检查权限Store
      </n-button>
    </div>
    
    <div style="margin: 20px 0;">
      <h2>测试结果</h2>
      <n-card>
        <pre>{{ debugInfo }}</pre>
      </n-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { usePermissionStore } from '@/store'
import { NButton, NCard } from 'naive-ui'

defineOptions({ name: '路由调试' })

const router = useRouter()
const permissionStore = usePermissionStore()
const debugInfo = ref('')

const testFunctionalDetailRoute = () => {
  debugInfo.value = '=== 测试功能测试计划详情路由 ===\n'

  const testPath = '/test_plan/functional_test_plan/detail/1'
  debugInfo.value += `测试路径: ${testPath}\n`

  // 检查路由是否存在
  const resolved = router.resolve(testPath)
  debugInfo.value += `路由解析结果:\n`
  debugInfo.value += `- 路径: ${resolved.path}\n`
  debugInfo.value += `- 名称: ${resolved.name}\n`
  debugInfo.value += `- 匹配的路由数量: ${resolved.matched.length}\n`

  if (resolved.matched.length === 0) {
    debugInfo.value += '❌ 路由未找到!\n'
  } else {
    debugInfo.value += '✅ 路由存在\n'

    resolved.matched.forEach((match, index) => {
      debugInfo.value += `路由 ${index + 1}:\n`
      debugInfo.value += `  - 名称: ${match.name}\n`
      debugInfo.value += `  - 路径: ${match.path}\n`
      debugInfo.value += `  - 组件: ${match.components ? Object.keys(match.components) : '无'}\n`

      // 检查组件是否正确
      if (match.components && match.components.default) {
        debugInfo.value += `  - 默认组件存在: ✅\n`
      } else {
        debugInfo.value += `  - 默认组件存在: ❌\n`
      }
    })
  }

  // 检查当前路由
  debugInfo.value += `\n当前路由: ${router.currentRoute.value.path}\n`
  debugInfo.value += `当前路由名称: ${router.currentRoute.value.name}\n`

  // 尝试跳转
  debugInfo.value += '\n开始跳转测试...\n'
  router.push(testPath).then(() => {
    debugInfo.value += '✅ 跳转成功\n'

    // 跳转后再次检查当前路由
    setTimeout(() => {
      debugInfo.value += `跳转后当前路径: ${router.currentRoute.value.path}\n`
      debugInfo.value += `跳转后当前路由名称: ${router.currentRoute.value.name}\n`
      debugInfo.value += `跳转后匹配的路由: ${router.currentRoute.value.matched.map(m => m.name).join(' -> ')}\n`
    }, 100)
  }).catch(error => {
    debugInfo.value += `❌ 跳转失败: ${error.message}\n`
  })
}

const checkAllRoutes = () => {
  debugInfo.value = '=== 检查所有注册的路由 ===\n'
  
  const allRoutes = router.getRoutes()
  debugInfo.value += `总路由数量: ${allRoutes.length}\n\n`
  
  // 查找测试计划相关路由
  const testPlanRoutes = allRoutes.filter(route => 
    route.path.includes('test_plan') || 
    (route.name && route.name.includes('测试计划'))
  )
  
  debugInfo.value += '测试计划相关路由:\n'
  testPlanRoutes.forEach(route => {
    debugInfo.value += `- ${route.name || '无名称'}: ${route.path}\n`
  })
  
  // 查找详情页面路由
  const detailRoutes = allRoutes.filter(route => 
    route.path.includes('/detail/') || route.path.includes('detail/:id')
  )
  
  debugInfo.value += '\n详情页面路由:\n'
  detailRoutes.forEach(route => {
    debugInfo.value += `- ${route.name || '无名称'}: ${route.path}\n`
  })
}

const checkPermissionStore = () => {
  debugInfo.value = '=== 检查权限Store ===\n'
  
  debugInfo.value += `访问路由数量: ${permissionStore.accessRoutes.length}\n`
  debugInfo.value += `菜单数量: ${permissionStore.menus.length}\n\n`
  
  // 查找测试计划相关的访问路由
  const testPlanAccessRoutes = permissionStore.accessRoutes.filter(route => 
    route.name === '测试计划' || route.path.includes('test_plan')
  )
  
  debugInfo.value += '测试计划访问路由:\n'
  testPlanAccessRoutes.forEach(route => {
    debugInfo.value += `- ${route.name}: ${route.path}\n`
    if (route.children) {
      route.children.forEach(child => {
        debugInfo.value += `  └─ ${child.name}: ${child.path} (隐藏: ${child.isHidden})\n`
        if (child.children) {
          child.children.forEach(grandchild => {
            debugInfo.value += `      └─ ${grandchild.name}: ${grandchild.path} (隐藏: ${grandchild.isHidden})\n`
          })
        }
      })
    }
  })
}
</script>
