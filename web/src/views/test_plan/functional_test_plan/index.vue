<template>
  <NLayout has-sider wh-full>
    <NLayoutSider
      bordered
      content-style="padding: 24px;"
      :collapsed-width="0"
      :width="240"
      show-trigger="arrow-circle"
    >
      <h1>项目列表</h1>
      <br />
      <NTree
        block-line
        :data="projectOption"
        key-field="id"
        label-field="name"
        default-expand-all
        :node-props="nodeProps"
        :selected-keys="selectedKeys"
      >
      </NTree>
    </NLayoutSider>
    <NLayoutContent>
      <CommonPage show-footer :title="selectedProjectName ? `${selectedProjectName} - 功能测试计划` : '功能测试计划'">
        <template #action>
          <div style="display: flex; gap: 12px; align-items: center;">
            <!-- 新建测试计划按钮 -->
            <NButton
              v-if="selectedProjectId"
              type="primary"
              @click="handleAdd"
            >
              <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新建测试计划
            </NButton>
          </div>
        </template>
        <!-- 表格 -->
        <CrudTable
          ref="$table"
          v-model:query-items="queryItems"
          :extra-params="extraParams"
          :scroll-x="1200"
          :columns="columns"
          :get-data="functionalTestPlanApi.getFunctionalTestPlanList"
          @reset="handleReset"
        >
          <template #queryBar>
            <QueryBarItem label="" :label-width="80">
              <NInput
                v-model:value="queryItems.plan_name"
                type="text"
                placeholder="请输入计划名称"
                clearable
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="50">
              <NSelect
                v-model:value="queryItems.status"
                placeholder="请选择状态"
                style="width: 120px"
                clearable
                :options="statusOptions"
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="50">
              <NSelect
                v-model:value="queryItems.level"
                placeholder="请选择等级"
                style="width: 80px"
                clearable
                :options="levelOptions"
              />
            </QueryBarItem>
          </template>
        </CrudTable>
      </CommonPage>
    </NLayoutContent>
  </NLayout>

  <!-- 新增/编辑弹窗 -->
  <CrudModal
    v-model:visible="modalVisible"
    :title="modalTitle"
    :loading="modalLoading"
    :show-footer="true"
    @save="handleSave"
    @close="handleModalClose"
  >
    <NForm
      ref="modalFormRef"
      label-placement="left"
      label-align="left"
      :label-width="80"
      :model="modalForm"
      :disabled="modalLoading"
    >
      <NFormItem label="计划名称" path="plan_name" :rule="{ required: true, message: '请输入计划名称' }">
        <NInput v-model:value="modalForm.plan_name" placeholder="请输入计划名称" />
      </NFormItem>
      <NFormItem label="等级" path="level">
        <NSelect v-model:value="modalForm.level" placeholder="请选择等级" :options="levelOptions" />
      </NFormItem>
      <NFormItem label="状态" path="status">
        <NSelect v-model:value="modalForm.status" placeholder="请选择状态" :options="statusOptions" />
      </NFormItem>
      <NFormItem label="项目" path="project_id" :rule="{ required: true, message: '请选择项目' }">
        <NSelect
          v-model:value="modalForm.project_id"
          placeholder="请选择项目"
          :options="projectSelectOptions"
          :disabled="!!selectedProjectId"
        />
      </NFormItem>
      <NFormItem label="计划描述" path="description">
        <NInput
          v-model:value="modalForm.description"
          type="textarea"
          placeholder="请输入计划描述"
          :autosize="{ minRows: 3, maxRows: 6 }"
        />
      </NFormItem>
    </NForm>
  </CrudModal>
</template>

<script setup>
import { h, onMounted, ref, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { NButton, NPopconfirm, NSpace, NTag, useMessage } from 'naive-ui'
import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import { useCRUD } from '@/composables'
import { formatDate, renderIcon } from '@/utils'
import functionalTestPlanApi from '@/api/functionalTestPlan'
import projectApi from '@/api/project'

defineOptions({ name: '功能测试计划' })

const router = useRouter()
const $message = useMessage()
const $table = ref(null)

// 项目相关状态
const projectOption = ref([])
const selectedProjectId = ref(null)
const selectedProjectName = ref('')
const selectedKeys = ref([])

// 查询条件
const queryItems = ref({
  plan_name: null,
  status: null,
  level: null,
  project_id: null,
})

// 额外参数（项目ID）
const extraParams = ref({})

// 状态选项
const statusOptions = [
  { label: '未开始', value: 'not_started' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'completed' }
]

// 等级选项
const levelOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' }
]

// 项目选择选项
const projectSelectOptions = ref([])

// 状态标签渲染
const renderStatus = (status) => {
  const statusMap = {
    'not_started': { type: 'default', text: '未开始' },
    'in_progress': { type: 'warning', text: '进行中' },
    'completed': { type: 'success', text: '已完成' }
  }
  const config = statusMap[status] || { type: 'default', text: status }
  return h(NTag, { type: config.type }, { default: () => config.text })
}

// 等级标签渲染
const renderLevel = (level) => {
  const levelMap = {
    'low': { type: 'success', text: '低' },
    'medium': { type: 'warning', text: '中' },
    'high': { type: 'error', text: '高' }
  }
  const config = levelMap[level] || { type: 'default', text: level }
  return h(NTag, { type: config.type }, { default: () => config.text })
}

// 项目树节点属性
const nodeProps = ({ option }) => {
  return {
    onClick() {
      handleProjectSelect(option)
    }
  }
}

// 通过率渲染
const renderPassRate = (passRate) => {
  if (passRate === null || passRate === undefined) {
    return h('span', { style: 'color: #999;' }, '暂无数据')
  }

  const rate = parseFloat(passRate)
  let color = '#999'
  if (rate >= 80) {
    color = '#52c41a' // 绿色
  } else if (rate >= 60) {
    color = '#faad14' // 橙色
  } else {
    color = '#ff4d4f' // 红色
  }

  return h('span', { style: `color: ${color}; font-weight: 500;` }, `${rate}%`)
}

// 表格列配置
const columns = [
  { title: '计划名称', key: 'plan_name', width: 200, ellipsis: { tooltip: true } },
  { title: '等级', key: 'level', width: 80, render: (row) => renderLevel(row.level) },
  { title: '状态', key: 'status', width: 100, render: (row) => renderStatus(row.status) },
  { title: '项目名称', key: 'project_name', width: 150, ellipsis: { tooltip: true } },
  { title: '通过率', key: 'pass_rate', width: 100, render: (row) => renderPassRate(row.pass_rate) },
  { title: '创建人', key: 'creator_name', width: 100 },
  { title: '创建时间', key: 'created_at', width: 180, render: (row) => formatDate(row.created_at) },
  { title: '更新时间', key: 'updated_at', width: 180, render: (row) => formatDate(row.updated_at) },
  {
    title: '操作',
    key: 'actions',
    width: 320,
    align: 'center',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return h(NSpace, { justify: 'center', size: 4, wrap: false }, {
        default: () => [
          h(NButton, {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => handleViewDetail(row)
          }, { default: () => '详情',  }),
          h(NButton, {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => handleEdit(row)
          }, { default: () => '编辑', }),
          h(NButton, {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => handleCopy(row)
          }, { default: () => '复制', }),
          h(NPopconfirm, {
            onPositiveClick: () => handleDelete(row),
            onNegativeClick: () => {}
          }, {
            default: () => '确认删除',
            trigger: () => h(NButton, {
              size: 'small',
              type: 'error',
              secondary: true
            }, { default: () => '删除' })
          })
        ]
      })
    }
  }
]

// CRUD配置
const {
  modalVisible,
  modalTitle,
  modalAction,
  modalLoading,
  handleSave: originalHandleSave,
  modalForm,
  modalFormRef,
  handleEdit: originalHandleEdit,
  handleDelete,
  handleAdd: originalHandleAdd,
  handleModalClose
} = useCRUD({
  name: '功能测试计划',
  initForm: {
    plan_name: '',
    level: 'medium',
    status: 'not_started',
    description: '',
    project_id: null,
  },
  doCreate: functionalTestPlanApi.createFunctionalTestPlan,
  doUpdate: functionalTestPlanApi.updateFunctionalTestPlan,
  doDelete: functionalTestPlanApi.deleteFunctionalTestPlan,
  refresh: () => $table.value?.handleSearch(),
})

// 处理项目选择
const handleProjectSelect = async (project) => {
  try {
    selectedProjectId.value = project.id
    selectedProjectName.value = project.name
    selectedKeys.value = [project.id]

    // 更新查询条件和额外参数
    queryItems.value.project_id = project.id
    extraParams.value = { project_id: project.id }

    // 刷新表格
    await nextTick()
    $table.value?.handleSearch()
  } catch (error) {
    console.error('选择项目失败:', error)
  }
}

// 加载项目列表
const loadProjectList = async () => {
  try {
    const response = await projectApi.getProjectList()
    projectOption.value = response.data || []
    projectSelectOptions.value = response.data?.map(item => ({
      label: item.name,
      value: item.id
    })) || []

    // 默认选择第一个项目
    if (projectOption.value.length > 0 && !selectedProjectId.value) {
      const firstProject = projectOption.value[0]
      selectedProjectId.value = firstProject.id
      selectedProjectName.value = firstProject.name
      selectedKeys.value = [firstProject.id]
      queryItems.value.project_id = firstProject.id
      extraParams.value = { project_id: firstProject.id }

      // 自动刷新表格显示第一个项目的数据
      await nextTick()
      $table.value?.handleSearch()
    }
  } catch (error) {
    console.error('加载项目列表失败:', error)
  }
}

// 新增处理
const handleAdd = () => {
  originalHandleAdd()
  if (selectedProjectId.value) {
    modalForm.value.project_id = selectedProjectId.value
  }
}

// 编辑处理
const handleEdit = (row) => {
  originalHandleEdit(row)
}

// 保存处理
const handleSave = async () => {
  await originalHandleSave()
}

// 复制处理
const handleCopy = async (row) => {
  try {
    await functionalTestPlanApi.copyFunctionalTestPlan({ id: row.id })
    $message.success('复制成功')
    $table.value?.handleSearch()
  } catch (error) {
    console.error('复制失败:', error)
    $message.error('复制失败')
  }
}

// 查看详情
const handleViewDetail = (row) => {
  console.log('=== 开始跳转到详情页面 ===')
  console.log('测试计划数据:', row)

  // 先检查所有注册的路由
  const allRoutes = router.getRoutes()
  console.log('所有注册的路由数量:', allRoutes.length)

  // 查找功能测试计划相关路由
  const functionalRoutes = allRoutes.filter(route =>
    route.name && route.name.includes('功能测试计划')
  )
  console.log('功能测试计划相关路由:', functionalRoutes)

  // 查找详情页面路由
  const detailRoutes = allRoutes.filter(route =>
    route.path && (route.path.includes('/detail/') || route.path.includes('detail/:id'))
  )
  console.log('详情页面路由:', detailRoutes)

  const targetPath = `/test_plan/functional_test_plan/detail/${row.id}`
  console.log('目标路径:', targetPath)

  // 检查路由是否存在
  const resolved = router.resolve(targetPath)
  console.log('路由解析结果:', resolved)

  if (resolved.matched.length === 0) {
    console.error('❌ 路由未找到!')
    $message.error('页面路由未找到，请检查路由配置')

    // 尝试手动添加路由进行测试
    console.log('尝试手动添加路由进行测试...')
    const testRoute = {
      name: '功能测试计划详情测试',
      path: '/test_plan/functional_test_plan/detail/:id',
      component: () => import('./detail/index.vue'),
      meta: {
        title: '功能测试计划详情',
      }
    }

    try {
      router.addRoute(testRoute)
      console.log('✅ 手动添加路由成功')

      // 再次尝试跳转
      router.push(targetPath).then(() => {
        console.log('✅ 跳转成功')
      }).catch(error => {
        console.error('❌ 跳转失败:', error)
        $message.error('页面跳转失败: ' + error.message)
      })
    } catch (error) {
      console.error('❌ 手动添加路由失败:', error)
      $message.error('路由添加失败: ' + error.message)
    }

    return
  }

  console.log('✅ 路由存在，开始跳转...')

  // 使用router跳转到详情页面
  router.push(targetPath).then(() => {
    console.log('✅ 跳转成功')
  }).catch(error => {
    console.error('❌ 跳转失败:', error)
    $message.error('页面跳转失败: ' + error.message)
  })
}

// 自定义重置处理 - 保持当前项目ID
const handleReset = async () => {
  console.log('=== 功能测试计划重置函数开始 ===')
  console.log('重置前 selectedProjectId:', selectedProjectId.value)
  console.log('重置前 queryItems:', queryItems.value)

  // 确保有选中的项目ID
  if (!selectedProjectId.value) {
    console.warn('没有选中的项目ID，使用默认重置逻辑')
    // 如果没有选中项目，清空所有查询条件
    queryItems.value = {
      plan_name: null,
      status: null,
      level: null,
      project_id: null
    }
    extraParams.value = {}
  } else {
    // 重置查询条件，但保持当前选中的项目ID
    queryItems.value = {
      plan_name: null,
      status: null,
      level: null,
      project_id: selectedProjectId.value
    }
    extraParams.value = { project_id: selectedProjectId.value }
  }

  console.log('重置后 queryItems:', queryItems.value)
  console.log('重置后 extraParams:', extraParams.value)

  // 等待响应式更新完成
  await nextTick()

  // 刷新表格数据
  $table.value?.handleSearch()
  console.log('=== 功能测试计划重置函数结束 ===')
}

// 监听 queryItems 变化，确保 project_id 不会被意外清空
watch(queryItems, (newVal, oldVal) => {
  console.log('功能测试计划 queryItems 变化:', { oldVal, newVal })

  // 如果 project_id 被清空但我们有选中的项目，则恢复它
  if (!newVal.project_id && selectedProjectId.value) {
    console.log('检测到 project_id 被清空，恢复为:', selectedProjectId.value)
    newVal.project_id = selectedProjectId.value
    extraParams.value = { project_id: selectedProjectId.value }
  }
}, { deep: true })

// 初始化
onMounted(() => {
  loadProjectList()
})
</script>
